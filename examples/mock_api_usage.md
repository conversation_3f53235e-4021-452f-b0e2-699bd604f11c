# Mock API Call Usage Examples

This document demonstrates how to use the mock API call functionality that has been added to the ops-admin-api.

## Overview

The mock API call functionality allows you to make GET requests to external APIs with an `x-api-key` header. This is useful for testing integrations, calling third-party services, or mocking external dependencies.

## Features

- Makes HTTP GET requests with custom headers
- Includes `x-api-key` header for API authentication
- Handles JSON and non-JSON responses
- Includes proper error handling and timeouts
- Provides comprehensive test coverage

## Code Structure

### 1. Service Layer (`internal/core/services/operation.go`)

The `MockAPICall` method has been added to the `OperationService`:

```go
func (s *OperationService) MockAPICall(url, apiKey string) (*MockAPIResponse, error)
```

### 2. Handler Layer (`internal/adapters/http/handlers/mock_api_handler.go`)

HTTP handlers for exposing the mock API functionality via REST endpoints.

### 3. Routes (`internal/adapters/http/routes/mock_api_routes.go`)

Route definitions for the mock API endpoints.

### 4. Tests (`internal/core/services/operation_mock_test.go`)

Comprehensive test suite covering various scenarios.

## API Endpoints

### 1. Get Example Usage
```bash
GET /api/v1/mock-api/example
Authorization: Bearer <jwt-token>
```

Returns documentation and examples for using the mock API endpoints.

### 2. Make Mock API Call (Predefined Example)
```bash
POST /api/v1/mock-api/call
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "url": "https://jsonplaceholder.typicode.com/posts/1",
  "api_key": "your-api-key-here"
}
```

### 3. Make Custom Mock API Call
```bash
POST /api/v1/mock-api/custom
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "url": "https://your-api-endpoint.com/data",
  "api_key": "your-api-key-here"
}
```

## Usage Examples

### Example 1: Basic Usage in Code

```go
// Create operation service instance
operationService := &OperationService{}

// Make a mock API call
response, err := operationService.MockAPICall(
    "https://jsonplaceholder.typicode.com/posts/1",
    "your-api-key-here",
)

if err != nil {
    log.Printf("API call failed: %v", err)
    return
}

log.Printf("API Response: %+v", response)
```

### Example 2: Using the HTTP Endpoint

```bash
# Get example documentation
curl -X GET http://localhost:8080/api/v1/mock-api/example \
  -H "Authorization: Bearer <your-jwt-token>"

# Make a mock API call
curl -X POST http://localhost:8080/api/v1/mock-api/call \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "url": "https://jsonplaceholder.typicode.com/posts/1",
    "api_key": "test-api-key-123"
  }'
```

### Example 3: Testing with Different APIs

```bash
# Test with a different API endpoint
curl -X POST http://localhost:8080/api/v1/mock-api/custom \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "url": "https://httpbin.org/get",
    "api_key": "test-key"
  }'
```

## Response Format

The mock API call returns a standardized response format:

```json
{
  "status": "success",
  "message": "Request completed successfully",
  "data": {
    // Response data from the external API
  }
}
```

## Error Handling

The implementation includes comprehensive error handling for:

- Invalid URLs
- Network timeouts
- HTTP error status codes
- JSON parsing errors
- Connection failures

## Testing

Run the test suite to verify functionality:

```bash
go test ./internal/core/services -v -run TestMockAPICall
```

## Security Considerations

1. **API Key Protection**: API keys are not logged or exposed in responses
2. **Input Validation**: URLs and API keys are validated before making requests
3. **Timeout Protection**: Requests have a 30-second timeout to prevent hanging
4. **Authentication Required**: All endpoints require JWT authentication

## Integration Notes

- The mock API functionality is integrated into the existing operation service
- It follows the same architectural patterns as other services in the application
- The implementation is compatible with the existing middleware and authentication system
- Routes are automatically registered when the server starts

## Future Enhancements

Potential improvements that could be added:

1. Support for other HTTP methods (POST, PUT, DELETE)
2. Custom header configuration
3. Request body support for POST/PUT requests
4. Response caching
5. Rate limiting for external API calls
6. Webhook support for async operations
